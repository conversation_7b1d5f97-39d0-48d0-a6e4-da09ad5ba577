2025-07-28 21:38:29.411 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 21:38:29.412 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 21:38:29.440 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 21:38:29.440 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 21:38:29.441 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 21:38:29.496 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 21:38:29.508 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 21:38:29.508 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 21:38:33.526 | b4a36acd9751477eb0c3916d2ec39f5c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 21:38:33.540 | 5a6da8b71068410f84900c44474b34ba | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 21:38:33.673 | 6cbea24eb8564a12b4fbe2f8ac393237 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 21:38:39.387 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1690: 实体对象 = True
2025-07-28 21:38:39.388 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211201_861.jpg
2025-07-28 21:38:39.388 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211201_861.jpg
2025-07-28 21:38:39.390 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1690
2025-07-28 21:38:39.392 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1689: 实体对象 = True
2025-07-28 21:38:39.392 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211201_591.jpg
2025-07-28 21:38:39.392 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211201_591.jpg
2025-07-28 21:38:39.393 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1689
2025-07-28 21:38:39.394 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1688: 实体对象 = True
2025-07-28 21:38:39.395 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211201_328.jpg
2025-07-28 21:38:39.395 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211201_328.jpg
2025-07-28 21:38:39.397 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1688
2025-07-28 21:38:39.398 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1687: 实体对象 = True
2025-07-28 21:38:39.399 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211201_081.jpg
2025-07-28 21:38:39.399 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211201_081.jpg
2025-07-28 21:38:39.400 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1687
2025-07-28 21:38:39.401 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1686: 实体对象 = True
2025-07-28 21:38:39.402 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211200_815.jpg
2025-07-28 21:38:39.402 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211200_815.jpg
2025-07-28 21:38:39.403 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1686
2025-07-28 21:38:39.405 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1685: 实体对象 = True
2025-07-28 21:38:39.405 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211200_563.jpg
2025-07-28 21:38:39.405 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211200_563.jpg
2025-07-28 21:38:39.407 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1685
2025-07-28 21:38:39.408 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1684: 实体对象 = True
2025-07-28 21:38:39.409 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211200_312.jpg
2025-07-28 21:38:39.409 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211200_312.jpg
2025-07-28 21:38:39.410 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1684
2025-07-28 21:38:39.411 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1683: 实体对象 = True
2025-07-28 21:38:39.412 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211200_058.jpg
2025-07-28 21:38:39.412 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211200_058.jpg
2025-07-28 21:38:39.413 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1683
2025-07-28 21:38:39.414 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1682: 实体对象 = True
2025-07-28 21:38:39.415 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211159_711.jpg
2025-07-28 21:38:39.415 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211159_711.jpg
2025-07-28 21:38:39.416 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1682
2025-07-28 21:38:39.417 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1681: 实体对象 = True
2025-07-28 21:38:39.418 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211159_520.jpg
2025-07-28 21:38:39.418 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211159_520.jpg
2025-07-28 21:38:39.419 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1681
2025-07-28 21:38:39.424 | 202d31d000744eda99f6759c5c1c8723 | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除10条记录，删除10个截图文件
2025-07-28 21:38:39.462 | 367230790b39470b9be95431fca14362 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 21:38:42.784 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1680: 实体对象 = True
2025-07-28 21:38:42.785 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211159_322.jpg
2025-07-28 21:38:42.785 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211159_322.jpg
2025-07-28 21:38:42.786 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1680
2025-07-28 21:38:42.787 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1679: 实体对象 = True
2025-07-28 21:38:42.788 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211159_134.jpg
2025-07-28 21:38:42.788 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211159_134.jpg
2025-07-28 21:38:42.789 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1679
2025-07-28 21:38:42.790 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1678: 实体对象 = True
2025-07-28 21:38:42.790 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211158_946.jpg
2025-07-28 21:38:42.790 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211158_946.jpg
2025-07-28 21:38:42.791 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1678
2025-07-28 21:38:42.792 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1677: 实体对象 = True
2025-07-28 21:38:42.793 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211158_765.jpg
2025-07-28 21:38:42.793 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211158_765.jpg
2025-07-28 21:38:42.794 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1677
2025-07-28 21:38:42.795 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1676: 实体对象 = True
2025-07-28 21:38:42.795 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211158_577.jpg
2025-07-28 21:38:42.795 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211158_577.jpg
2025-07-28 21:38:42.797 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1676
2025-07-28 21:38:42.798 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1675: 实体对象 = True
2025-07-28 21:38:42.798 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211158_372.jpg
2025-07-28 21:38:42.799 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211158_372.jpg
2025-07-28 21:38:42.799 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1675
2025-07-28 21:38:42.800 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1674: 实体对象 = True
2025-07-28 21:38:42.801 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211158_172.jpg
2025-07-28 21:38:42.801 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211158_172.jpg
2025-07-28 21:38:42.802 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1674
2025-07-28 21:38:42.803 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1673: 实体对象 = True
2025-07-28 21:38:42.804 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211157_979.jpg
2025-07-28 21:38:42.804 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211157_979.jpg
2025-07-28 21:38:42.805 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1673
2025-07-28 21:38:42.806 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1672: 实体对象 = True
2025-07-28 21:38:42.806 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211157_775.jpg
2025-07-28 21:38:42.806 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211157_775.jpg
2025-07-28 21:38:42.807 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1672
2025-07-28 21:38:42.809 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1671: 实体对象 = True
2025-07-28 21:38:42.809 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211157_594.jpg
2025-07-28 21:38:42.809 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211157_594.jpg
2025-07-28 21:38:42.810 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1671
2025-07-28 21:38:42.814 | 06c7aa0d455e4d718f420e2b17830b81 | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除10条记录，删除10个截图文件
2025-07-28 21:38:42.842 | a762042686534c539b94f415ef7bb333 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 21:38:52.281 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1670: 实体对象 = True
2025-07-28 21:38:52.282 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211157_001.jpg
2025-07-28 21:38:52.282 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211157_001.jpg
2025-07-28 21:38:52.284 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1670
2025-07-28 21:38:52.285 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1669: 实体对象 = True
2025-07-28 21:38:52.285 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211156_221.jpg
2025-07-28 21:38:52.285 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211156_221.jpg
2025-07-28 21:38:52.286 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1669
2025-07-28 21:38:52.288 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1668: 实体对象 = True
2025-07-28 21:38:52.288 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211153_603.jpg
2025-07-28 21:38:52.288 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211153_603.jpg
2025-07-28 21:38:52.289 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1668
2025-07-28 21:38:52.290 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1667: 实体对象 = True
2025-07-28 21:38:52.291 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211153_404.jpg
2025-07-28 21:38:52.291 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211153_404.jpg
2025-07-28 21:38:52.292 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1667
2025-07-28 21:38:52.293 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1666: 实体对象 = True
2025-07-28 21:38:52.294 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211153_202.jpg
2025-07-28 21:38:52.294 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211153_202.jpg
2025-07-28 21:38:52.295 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1666
2025-07-28 21:38:52.296 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1665: 实体对象 = True
2025-07-28 21:38:52.296 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211153_015.jpg
2025-07-28 21:38:52.296 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211153_015.jpg
2025-07-28 21:38:52.297 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1665
2025-07-28 21:38:52.298 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1664: 实体对象 = True
2025-07-28 21:38:52.299 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211150_005.jpg
2025-07-28 21:38:52.299 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211150_005.jpg
2025-07-28 21:38:52.300 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1664
2025-07-28 21:38:52.301 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1663: 实体对象 = True
2025-07-28 21:38:52.302 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211149_813.jpg
2025-07-28 21:38:52.302 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211149_813.jpg
2025-07-28 21:38:52.303 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1663
2025-07-28 21:38:52.304 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1662: 实体对象 = True
2025-07-28 21:38:52.304 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211149_613.jpg
2025-07-28 21:38:52.305 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211149_613.jpg
2025-07-28 21:38:52.306 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1662
2025-07-28 21:38:52.307 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1661: 实体对象 = True
2025-07-28 21:38:52.307 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211149_408.jpg
2025-07-28 21:38:52.307 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211149_408.jpg
2025-07-28 21:38:52.308 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1661
2025-07-28 21:38:52.311 | 1875d8f8ea164a21984862388fb5166d | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除10条记录，删除10个截图文件
2025-07-28 21:38:52.339 | dc67385d3a1342b2992feeb73a85800e | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 21:38:54.870 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1660: 实体对象 = True
2025-07-28 21:38:54.871 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211149_139.jpg
2025-07-28 21:38:54.871 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211149_139.jpg
2025-07-28 21:38:54.874 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1660
2025-07-28 21:38:54.877 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1659: 实体对象 = True
2025-07-28 21:38:54.878 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211148_875.jpg
2025-07-28 21:38:54.878 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211148_875.jpg
2025-07-28 21:38:54.880 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1659
2025-07-28 21:38:54.882 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1658: 实体对象 = True
2025-07-28 21:38:54.883 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211148_609.jpg
2025-07-28 21:38:54.883 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211148_609.jpg
2025-07-28 21:38:54.885 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1658
2025-07-28 21:38:54.887 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1657: 实体对象 = True
2025-07-28 21:38:54.888 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211148_346.jpg
2025-07-28 21:38:54.888 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211148_346.jpg
2025-07-28 21:38:54.890 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1657
2025-07-28 21:38:54.892 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1656: 实体对象 = True
2025-07-28 21:38:54.893 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211148_079.jpg
2025-07-28 21:38:54.893 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211148_079.jpg
2025-07-28 21:38:54.895 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1656
2025-07-28 21:38:54.897 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1655: 实体对象 = True
2025-07-28 21:38:54.898 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211147_812.jpg
2025-07-28 21:38:54.898 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211147_812.jpg
2025-07-28 21:38:54.900 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1655
2025-07-28 21:38:54.902 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1654: 实体对象 = True
2025-07-28 21:38:54.903 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211147_550.jpg
2025-07-28 21:38:54.903 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211147_550.jpg
2025-07-28 21:38:54.905 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1654
2025-07-28 21:38:54.908 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1653: 实体对象 = True
2025-07-28 21:38:54.908 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211147_284.jpg
2025-07-28 21:38:54.909 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211147_284.jpg
2025-07-28 21:38:54.910 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1653
2025-07-28 21:38:54.912 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1652: 实体对象 = True
2025-07-28 21:38:54.913 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211147_023.jpg
2025-07-28 21:38:54.913 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211147_023.jpg
2025-07-28 21:38:54.915 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1652
2025-07-28 21:38:54.917 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1651: 实体对象 = True
2025-07-28 21:38:54.918 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211146_757.jpg
2025-07-28 21:38:54.918 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211146_757.jpg
2025-07-28 21:38:54.921 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1651
2025-07-28 21:38:54.925 | 55901168bcd74b389ca1c03c32f6e156 | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除10条记录，删除10个截图文件
2025-07-28 21:38:54.980 | bbdaef85a044475cadd96dac7e6542e3 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 21:38:58.043 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1650: 实体对象 = True
2025-07-28 21:38:58.043 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211146_505.jpg
2025-07-28 21:38:58.043 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211146_505.jpg
2025-07-28 21:38:58.045 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1650
2025-07-28 21:38:58.047 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1649: 实体对象 = True
2025-07-28 21:38:58.047 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211146_241.jpg
2025-07-28 21:38:58.047 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211146_241.jpg
2025-07-28 21:38:58.048 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1649
2025-07-28 21:38:58.049 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1648: 实体对象 = True
2025-07-28 21:38:58.049 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211145_989.jpg
2025-07-28 21:38:58.050 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211145_989.jpg
2025-07-28 21:38:58.051 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1648
2025-07-28 21:38:58.052 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1647: 实体对象 = True
2025-07-28 21:38:58.053 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211145_730.jpg
2025-07-28 21:38:58.053 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211145_730.jpg
2025-07-28 21:38:58.054 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1647
2025-07-28 21:38:58.055 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1646: 实体对象 = True
2025-07-28 21:38:58.055 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211145_478.jpg
2025-07-28 21:38:58.055 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211145_478.jpg
2025-07-28 21:38:58.056 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1646
2025-07-28 21:38:58.058 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1645: 实体对象 = True
2025-07-28 21:38:58.058 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211145_219.jpg
2025-07-28 21:38:58.058 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211145_219.jpg
2025-07-28 21:38:58.059 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1645
2025-07-28 21:38:58.060 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1644: 实体对象 = True
2025-07-28 21:38:58.061 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211144_970.jpg
2025-07-28 21:38:58.061 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211144_970.jpg
2025-07-28 21:38:58.062 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1644
2025-07-28 21:38:58.063 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1643: 实体对象 = True
2025-07-28 21:38:58.064 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211144_719.jpg
2025-07-28 21:38:58.064 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211144_719.jpg
2025-07-28 21:38:58.065 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1643
2025-07-28 21:38:58.066 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1642: 实体对象 = True
2025-07-28 21:38:58.066 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211144_466.jpg
2025-07-28 21:38:58.066 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211144_466.jpg
2025-07-28 21:38:58.067 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1642
2025-07-28 21:38:58.068 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1641: 实体对象 = True
2025-07-28 21:38:58.069 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211144_217.jpg
2025-07-28 21:38:58.069 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211144_217.jpg
2025-07-28 21:38:58.070 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1641
2025-07-28 21:38:58.080 | 84b5bee88b524653a8a788ddaf50066b | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除10条记录，删除10个截图文件
2025-07-28 21:38:58.108 | 5de0aec96ac349b3b0225af24f71b22c | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 21:39:03.252 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1640: 实体对象 = True
2025-07-28 21:39:03.253 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211143_963.jpg
2025-07-28 21:39:03.253 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211143_963.jpg
2025-07-28 21:39:03.254 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1640
2025-07-28 21:39:03.255 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1639: 实体对象 = True
2025-07-28 21:39:03.255 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211143_705.jpg
2025-07-28 21:39:03.256 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211143_705.jpg
2025-07-28 21:39:03.257 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1639
2025-07-28 21:39:03.258 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1638: 实体对象 = True
2025-07-28 21:39:03.259 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211143_514.jpg
2025-07-28 21:39:03.259 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211143_514.jpg
2025-07-28 21:39:03.260 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1638
2025-07-28 21:39:03.261 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1637: 实体对象 = True
2025-07-28 21:39:03.261 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211143_329.jpg
2025-07-28 21:39:03.261 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211143_329.jpg
2025-07-28 21:39:03.262 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1637
2025-07-28 21:39:03.263 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1636: 实体对象 = True
2025-07-28 21:39:03.264 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211143_151.jpg
2025-07-28 21:39:03.264 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211143_151.jpg
2025-07-28 21:39:03.265 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1636
2025-07-28 21:39:03.266 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1635: 实体对象 = True
2025-07-28 21:39:03.267 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211142_972.jpg
2025-07-28 21:39:03.267 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211142_972.jpg
2025-07-28 21:39:03.268 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1635
2025-07-28 21:39:03.269 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1634: 实体对象 = True
2025-07-28 21:39:03.269 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211142_778.jpg
2025-07-28 21:39:03.270 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211142_778.jpg
2025-07-28 21:39:03.271 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1634
2025-07-28 21:39:03.272 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1633: 实体对象 = True
2025-07-28 21:39:03.272 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211142_604.jpg
2025-07-28 21:39:03.272 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211142_604.jpg
2025-07-28 21:39:03.273 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1633
2025-07-28 21:39:03.274 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1632: 实体对象 = True
2025-07-28 21:39:03.275 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_211142_402.jpg
2025-07-28 21:39:03.275 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_211142_402.jpg
2025-07-28 21:39:03.276 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1632
2025-07-28 21:39:03.279 | 2dee7205fea64e32be60ed59905c422c | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除9条记录，删除9个截图文件
2025-07-28 21:39:03.308 | fba0904821ee4d288154fafe9ff65906 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 21:39:06.525 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 21:39:06.526 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 21:39:06.526 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 21:39:06.526 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 21:39:06.526 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 21:39:06.526 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 21:39:06.526 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 21:39:06.526 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 21:39:06.527 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 21:39:06.527 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 21:39:06.532 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 21:39:06.532 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 21:39:06.532 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 21:39:06.532 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 21:39:06.534 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 21:39:06.534 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 21:39:06.534 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 21:39:06.534 | c3a035cbc53b4a01b3db04d264600b20 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 21:39:08.974 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:start_task:204 - 开始启动任务: 12
2025-07-28 21:39:08.976 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-28 21:39:08.977 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:505 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-28 21:39:08.977 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:512 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-28 21:39:08.977 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:519 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 21:39:08.977 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:525 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 21:39:08.977 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:526 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-28 21:39:08.977 | 7e9ef3a7806e436385508fa1947b82d4 | WARNING  | module_stream.service.task_execution_service:_validate_required_config:640 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-28 21:39:08.978 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:664 - 验证模型初始化 - 添加YOLOv5路径: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master
2025-07-28 21:39:08.978 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:679 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-28 21:39:08.978 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:684 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master\\utils', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition\\RuoYi-Vue3-FastAPI-master\\ruoyi-fastapi-backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs']
2025-07-28 21:39:08.979 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:685 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-28 21:39:08.979 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:689 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 21:39:10.251 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:710 - 验证模型初始化 - 成功导入智驱力模型
2025-07-28 21:39:10.251 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 21:39:10.251 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 21:39:10.251 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 21:39:10.251 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 21:39:10.251 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:722 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 21:39:12.561 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:734 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-28 21:39:12.561 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:735 -    - 设备: cuda
2025-07-28 21:39:12.561 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:736 -    - 图像尺寸: 640
2025-07-28 21:39:12.562 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:737 -    - 置信度阈值: 0.01
2025-07-28 21:39:12.562 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:738 -    - NMS阈值: 0.5
2025-07-28 21:39:12.563 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:751 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-28 21:39:12.576 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:start_monitor_stream:4094 - 任务 12 的监控流已启动
2025-07-28 21:39:12.578 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_cache_task_config:112 - 任务12配置已缓存
2025-07-28 21:39:12.579 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:detection_loop:799 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-28 21:39:12.579 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:detection_loop:829 - 成功预导入YOLOv5 utils模块
2025-07-28 21:39:12.579 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:detection_loop:835 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 21:39:12.580 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: zql_detect
2025-07-28 21:39:12.581 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: model
2025-07-28 21:39:12.581 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:detection_loop:855 - 成功导入智驱力模型
2025-07-28 21:39:12.581 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 21:39:12.581 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-28 21:39:12.581 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-28 21:39:12.582 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 21:39:12.582 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:detection_loop:867 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 21:39:12.691 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:detection_loop:880 - 智驱力模型初始化成功
2025-07-28 21:39:12.691 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:detection_loop:881 -    - 设备: cuda
2025-07-28 21:39:12.691 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:detection_loop:882 -    - 图像尺寸: 640
2025-07-28 21:39:12.691 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:detection_loop:883 -    - 置信度阈值: 0.01
2025-07-28 21:39:12.691 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:detection_loop:884 -    - NMS阈值: 0.5
2025-07-28 21:39:12.692 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:detection_loop:897 - 智驱力后处理器初始化成功
2025-07-28 21:39:12.752 | 7e9ef3a7806e436385508fa1947b82d4 | ERROR    | module_stream.service.task_execution_service:detection_loop:917 - 视频流连接超时或失败: rtsp://127.0.0.1:8554/test1
2025-07-28 21:39:12.754 | 7e9ef3a7806e436385508fa1947b82d4 | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3287 - 更新任务状态失败: readexactly() called while another coroutine is already waiting for incoming data
2025-07-28 21:39:12.754 | 7e9ef3a7806e436385508fa1947b82d4 | ERROR    | module_stream.service.task_execution_service:detection_loop:1280 - 检测过程中发生错误: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-07-28 21:39:12.755 | 7e9ef3a7806e436385508fa1947b82d4 | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3287 - 更新任务状态失败: This session is in 'prepared' state; no further SQL can be emitted within this transaction.
2025-07-28 21:39:12.756 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.service.task_execution_service:start_task:255 - 任务 12 启动成功，包括实时监控流
2025-07-28 21:39:12.757 | 7e9ef3a7806e436385508fa1947b82d4 | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-28 21:39:12.768 | 4c2b7a083af94a82baba048123725547 | ERROR    | exceptions.handle:exception_handler:117 - 数据库连接错误: (asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-28 21:39:32.410 | 39030c7214ed48bd9747923b149a95a8 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 21:39:32.426 | bbdcc90601d84afb94aa25c67a3f59cc | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 21:39:32.536 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 21:39:32.536 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 21:39:32.536 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 21:39:32.537 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 21:39:32.537 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 21:39:32.537 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 21:39:32.537 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 21:39:32.538 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 21:39:32.538 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 21:39:32.538 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 21:39:32.549 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 21:39:32.549 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 21:39:32.549 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 21:39:32.550 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 21:39:32.552 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 21:39:32.553 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 21:39:32.553 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 21:39:32.553 | a40825420b46428fa6f73bd2a467ee06 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 21:39:35.301 | 0b75643856fa40a98f011d6189d7fc54 | INFO     | module_stream.service.task_execution_service:stop_task:306 - 开始停止任务: 12
2025-07-28 21:39:35.303 | 0b75643856fa40a98f011d6189d7fc54 | INFO     | module_stream.service.task_execution_service:stop_task:330 - 已从运行任务列表移除: 12
2025-07-28 21:39:35.303 | 0b75643856fa40a98f011d6189d7fc54 | INFO     | module_stream.service.task_execution_service:stop_task:339 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-28 21:39:35.303 | 0b75643856fa40a98f011d6189d7fc54 | INFO     | module_stream.service.task_execution_service:stop_monitor_stream:4118 - 任务 12 的监控流已停止
2025-07-28 21:39:35.303 | 0b75643856fa40a98f011d6189d7fc54 | INFO     | module_stream.service.task_execution_service:stop_task:344 - 监控流停止成功: 12
2025-07-28 21:39:35.303 | 0b75643856fa40a98f011d6189d7fc54 | INFO     | module_stream.service.task_execution_service:_clear_task_cache:133 - 任务12缓存已清除
2025-07-28 21:39:35.303 | 0b75643856fa40a98f011d6189d7fc54 | INFO     | module_stream.service.task_execution_service:stop_task:353 - 任务缓存清理成功: 12
2025-07-28 21:39:35.308 | 0b75643856fa40a98f011d6189d7fc54 | INFO     | module_stream.service.task_execution_service:stop_task:362 - 任务状态更新成功: 12
2025-07-28 21:39:35.308 | 0b75643856fa40a98f011d6189d7fc54 | INFO     | module_stream.service.task_execution_service:stop_task:374 - 任务 12 停止成功，包括实时监控流
2025-07-28 21:39:35.322 | 0b75643856fa40a98f011d6189d7fc54 | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-28 21:39:35.337 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 21:39:35.338 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 21:39:35.338 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 21:39:35.338 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 21:39:35.338 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 21:39:35.338 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 21:39:35.338 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 21:39:35.339 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 21:39:35.339 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 21:39:35.339 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 21:39:35.341 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 21:39:35.341 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 21:39:35.341 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 21:39:35.342 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 21:39:35.343 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 21:39:35.343 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 21:39:35.343 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 21:39:35.343 | 369115a3133e4ad095c3bb4cb13edc17 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 21:40:34.424 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 21:40:34.424 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
